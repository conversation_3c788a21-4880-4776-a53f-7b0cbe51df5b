// TypeBox schema for logins service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {commonFields, nullable, queryWrapper, emailHandler, commonPatch} from '../../utils/common/typebox-schemas.js'
import {passwordHash} from '@feathersjs/authentication-local';

export const loginsSchema = Type.Object(
    {
      _id: ObjectIdSchema(), // required

      name: Type.Optional(Type.String()),
      email: Type.Optional(Type.String()),
      phone: Type.Optional(Type.String()),
      password: Type.Optional(Type.String()),
      pendingPassword: Type.Optional(nullable.string),
      did: Type.Optional(Type.String()),
      ucan: Type.Optional(Type.String()),
      fingerprints: Type.Optional(Type.Array(ObjectIdSchema())),
      owner: Type.Optional(ObjectIdSchema()), // ppls

      keyPair: Type.Optional(
          Type.Object(
              {
                publicKey: Type.Optional(Type.String()),
                privateKey: Type.Optional(Type.String()),
                alg: Type.Optional(Type.String()),
              },
              { additionalProperties: true }
          )
      ),

      googleId: Type.Optional(Type.String()),
      facebookId: Type.Optional(Type.String()),
      twitterId: Type.Optional(Type.String()),
      linkedinId: Type.Optional(Type.String()),
      microsoftId: Type.Optional(Type.String()),
      githubId: Type.Optional(Type.String()),
      appleId: Type.Optional(Type.String()),

      isVerified: Type.Optional(Type.Boolean()),
      verifyToken: Type.Optional(nullable.string),
      verifyExpires: Type.Optional(Type.Any()),
      lastLogin: Type.Optional(Type.Any()),
      loginAttempts: Type.Optional(Type.Array(Type.Any())),
      locked: Type.Optional(Type.Any()),
      lastLoginMethod: Type.Optional(Type.String()),
      resetToken: Type.Optional(nullable.string),
      resetExpires: Type.Optional(
          Type.Union([Type.String(), Type.Object({}, { additionalProperties: true }), Type.Null()])
      ),

      // spread in shared common fields
      ...commonFields,
    },
    {
      $id: 'Logins',
      additionalProperties: true,
      required: ['_id'],
    }
);

export type Logins = Static<typeof loginsSchema>
export const loginsValidator = getValidator(loginsSchema, dataValidator)
export const loginsResolver = resolve<Logins, HookContext>({})
export const loginsExternalResolver = resolve<Logins, HookContext>({})


const allDataResolvers = () => {
  return ({
    password: passwordHash({strategy: 'local'}) as any,
    pendingPassword: async (val:any, data, context):Promise<null|string|undefined> => {
      if (val) return passwordHash({strategy: 'local'})(val, data, context);
      return val
    },
    email: emailHandler({ throw: true })
  })
}


// Schema for creating new data
export const loginsDataSchema = Type.Object({
  ...Type.Omit(loginsSchema, ['_id']).properties
}, { additionalProperties: false })

export type LoginsData = Static<typeof loginsDataSchema>
export const loginsDataValidator = getValidator(loginsDataSchema, dataValidator)
export const loginsDataResolver = resolve<LoginsData, HookContext>({
  properties: {
    ...allDataResolvers()
  }
})

const loginsQueryProperties = Type.Pick(loginsSchema, ['_id', 'owner', 'fingerprints'])

const loginsPatchSchema = commonPatch(loginsSchema, { pushPullOpts: [], pickedForSet: loginsQueryProperties })

export type LoginsPatch = Static<typeof loginsPatchSchema>
export const loginsPatchValidator = getValidator(loginsPatchSchema, dataValidator)
export const loginsPatchResolver = resolve<LoginsPatch, HookContext>({
  properties: {
    ...allDataResolvers()
  }

})

export const loginsQuerySchema = queryWrapper(loginsQueryProperties)

export type LoginsQuery = Static<typeof loginsQuerySchema>
export const loginsQueryValidator = getValidator(loginsQuerySchema, queryValidator)
export const loginsQueryResolver = resolve<LoginsQuery, HookContext>({})
