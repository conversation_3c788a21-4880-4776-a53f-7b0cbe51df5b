

import { Type, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import { stateIds } from './states.js'

// Nullable utility types
export const Nullable = {
  String: Type.Union([Type.String(), Type.Null()]),
  Boolean: Type.Union([Type.Boolean(), Type.Null()])
}


// RRule schema for recurring events
export const rRuleSchema = Type.Object({
  freq: Type.Optional(Type.String()),
  dtstart: Type.String(),
  until: Type.Optional(Type.String()),
  interval: Type.Optional(Type.Number()),
  count: Type.Optional(Type.Number()),
  bymonth: Type.Optional(Type.Array(Type.Number())),
  byweekday: Type.Optional(Type.Array(Type.String())),
  byhour: Type.Optional(Type.Array(Type.Number())),
  byminute: Type.Optional(Type.Array(Type.Number())),
  bysecond: Type.Optional(Type.Array(Type.Number())),
  bymonthday: Type.Optional(Type.Array(Type.Number())),
  byyearday: Type.Optional(Type.Array(Type.Number())),
  byweekno: Type.Optional(Type.Array(Type.Number())),
  bysetpos: Type.Optional(Type.Array(Type.Number())),
  wkst: Type.Optional(Type.String()),
  tzid: Type.Optional(Type.String())
}, { additionalProperties: false })

// Mandate schema for legal agreements
export const mandateSchema = Type.Object({
  acceptedAt: Type.Optional(Type.Any()),
  ip: Type.Optional(Type.String()),
  ua: Type.Optional(Type.String()),
  copy: Type.Optional(Type.String()),
  fingerprint: ObjectIdSchema(),
  login: ObjectIdSchema(),
  email: Type.Optional(Type.String()),
  phone: Type.Optional(Type.String()),
  signature: Type.Optional(Type.String())
}, { additionalProperties: false })

// Phone number schema
export const phoneSchema = Type.Object({
  number: Type.Optional(Type.Object({
    input: Type.Optional(Type.String()),
    international: Type.Optional(Type.String()),
    national: Type.Optional(Type.String()),
    e164: Type.Optional(Type.String()),
    rfc3966: Type.Optional(Type.String()),
    significant: Type.Optional(Type.String())
  }, { additionalProperties: false })),
  regionCode: Type.Optional(Type.String()),
  valid: Type.Optional(Type.Boolean()),
  possible: Type.Optional(Type.Boolean()),
  possibility: Type.Optional(Type.String()),
  countryCode: Type.Optional(Type.Number()),
  canBeInternationallyDialled: Type.Optional(Type.Boolean()),
  typeIsMobile: Type.Optional(Type.Boolean()),
  typeIsFixedLine: Type.Optional(Type.Boolean())
}, { additionalProperties: true })

export type Phone = Static<typeof phoneSchema>

// GeoJSON schemas
export const geoJsonFeatureSchema = Type.Object({
  name: Type.Optional(Type.String()),
  type: Type.Optional(Type.Literal('Feature')),
  geometry: Type.Optional(Type.Object({
    type: Type.Optional(Type.String()),
    coordinates: Type.Optional(Type.Array(Type.Any()))
  }, { additionalProperties: true })),
  properties: Type.Optional(Type.Record(Type.String(), Type.Any())),
  addresses: Type.Optional(Type.Array(Type.Any()))
}, { additionalProperties: true })

export type GeoJsonFeature = Static<typeof geoJsonFeatureSchema>

export const geoJsonSchema = Type.Object({
  type: Type.Optional(Type.String()),
  allowFeatures: Type.Optional(Type.Array(Type.String())),
  features: Type.Optional(Type.Array(geoJsonFeatureSchema))
}, { additionalProperties: true })

export type GeoJson = Static<typeof geoJsonSchema>

// Image/File upload schema
export const imageSchema = Type.Object({
  uploadId: ObjectIdSchema(),
  fileId: Type.Optional(Type.String()),
  storage: Type.Optional(Type.String()),
  appPath: Type.Optional(Nullable.Boolean),
  info: Type.Optional(Type.Object({
    name: Type.Optional(Type.String()),
    size: Type.Optional(Type.Number()),
    type: Type.Optional(Type.String()),
    lastModifiedDate: Type.Optional(Type.Any())
  }, { additionalProperties: false })),
  subPath: Type.Optional(Type.Union([Type.Array(Type.String()), Type.Null()])),
  url: Type.Optional(Type.String())
}, { additionalProperties: true })

export type Image = Static<typeof imageSchema>

// Video schema extends image schema
export const videoSchema = Type.Intersect([
  imageSchema,
  Type.Object({
    title: Type.Optional(Type.String()),
    author_name: Type.Optional(Type.String()),
    author_url: Type.Optional(Type.String()),
    type: Type.Optional(Type.String()),
    height: Type.Optional(Type.Any()),
    width: Type.Optional(Type.Any()),
    version: Type.Optional(Type.Any()),
    provider_name: Type.Optional(Type.String()),
    provider_url: Type.Optional(Type.String()),
    thumbnail_height: Type.Optional(Type.Number()),
    thumbnail_width: Type.Optional(Type.Number()),
    thumbnail_url: Type.Optional(Type.String())
  })
])

// Updates tracking schema
export const updatesSchema = Type.Object({
  did: Type.Optional(Nullable.String),
  login: Type.Union([ObjectIdSchema(), Type.Null(), Type.String()]),
  fingerprint: Type.Union([ObjectIdSchema(), Type.Null(), Type.String()]),
  origin: Type.Optional(Nullable.String),
  longtail: Type.Optional(Nullable.String),
  at: Type.Optional(Type.Any())
}, { additionalProperties: true })

export type Updates = Static<typeof updatesSchema>

const updatedByHistoryEntrySchema = Type.Optional(Type.Intersect([
  updatesSchema,
  Type.Object({ updatedAt: Type.Optional(Type.Any()) })
]))

const UpdatedByHistorySchema = Type.Array(updatedByHistoryEntrySchema)

// Common fields that appear in all entities - structured for both spreading and Pick operations
const commonFieldsProperties = {
  env: Type.Optional(ObjectIdSchema()),
  host: Type.Optional(ObjectIdSchema()),
  ref: Type.Optional(ObjectIdSchema()),
  changeLog: Type.Optional(ObjectIdSchema()),
  editMap: Type.Optional(Type.Record(Type.String(), Type.Any())),
  deleted: Type.Optional(Type.Boolean()),
  session_fp: Type.Optional(Type.String()),
  deletedAt: Type.Optional(Type.Any()),
  updatedAt: Type.Optional(Type.Any()),
  createdAt: Type.Optional(Type.Any()),
  createdBy: Type.Optional(updatesSchema),
  updatedBy: Type.Optional(updatesSchema),
  updatedByHistory: Type.Optional(UpdatedByHistorySchema)
}

// For spreading into schemas
export const commonFields = commonFieldsProperties

// Address schema
export const addressSchema = Type.Object({
  id: Type.Optional(Type.String()),
  address1: Type.Optional(Type.String()),
  address2: Type.Optional(Type.String()),
  formatted: Type.Optional(Type.String()),
  postal: Type.Optional(Type.String()),
  city: Type.Optional(Type.String()),
  region: Type.Optional(Type.String()),
  country: Type.Optional(Type.String()),
  latitude: Type.Optional(Type.Number()),
  longitude: Type.Optional(Type.Number()),
  googleAddress: Type.Optional(Type.Record(Type.String(), Type.Any())),
  name: Type.Optional(Type.String()),
  tags: Type.Optional(Type.Record(Type.String(), Type.Any())),
  type: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: true })

export const serviceAddressSchema = Type.Intersect([
  addressSchema,
  Type.Object({
    geo: Type.Optional(geoJsonSchema)
  })
])

// Utility for "effectively any" type
export const EffectiveAny = Type.Union([
  Type.String(),
  Type.Record(Type.String(), Type.Any()),
  Type.Array(Type.Any()),
  Type.Number()
])

// Tax-related schemas
const taxItemSchema = Type.Object({
  name: Type.Optional(Type.String()),
  type: Type.Optional(Type.Union([Type.Literal('percent'), Type.Literal('flat')])),
  rate: Type.Optional(Type.Number()),
  notes: Type.Optional(Type.String())
}, { additionalProperties: false })

export const taxSchema = Type.Object({
  automateTaxes: Type.Optional(Type.Boolean()),
  taxExempt: Type.Optional(Type.Boolean()),
  origin: Type.Optional(Type.Boolean()),
  taxOverrides: Type.Optional(Type.Array(taxItemSchema)),
  taxes: Type.Optional(Type.Array(Type.Object({
    name: Type.Optional(Type.String()),
    isDefault: Type.Optional(Type.Boolean()),
    origin: Type.Optional(Type.Boolean()),
    areaId: Type.Optional(Type.String()),
    states: Type.Optional(Type.Array(Type.Union(stateIds.map(id => Type.Literal(id))))),
    postal_codes: Type.Optional(Type.Array(Type.String())),
    cities: Type.Optional(Type.Array(Type.String())),
    taxes: Type.Optional(Type.Array(taxItemSchema))
  }, { additionalProperties: false })))
}, { additionalProperties: false })

// Common query properties
export const commonQueriesSchema = Type.Object({
  // $select: Type.Optional(Type.Any()),
  // $regex: Type.Optional(Type.Any()),
  // $text: Type.Optional(Type.Any()),
  // $options: Type.Optional(Type.Any()),
  // $search: Type.Optional(Type.Any()),
  // $elemMatch: Type.Optional(Type.Any()),
  // _limit_to: Type.Optional(Type.Any()),
  'updatedBy.login': Type.Optional(ObjectIdSchema()),
  'createdBy.login': Type.Optional(ObjectIdSchema())
}, { additionalProperties: true })

// ID list query schema
export const idListQuerySchema = Type.Union([
  ObjectIdSchema(),
  Type.Array(ObjectIdSchema())
])


// Helper to create $push/$addToSet operations
export const createAddToSetSchema = (opts: Array<{ path: string, type: any }>) => {
  const properties: Record<string, any> = {}

  // Always include updatedByHistory
  const allOpts = [
    { path: 'updatedByHistory', type: updatedByHistoryEntrySchema },
    ...opts
  ]

  allOpts.forEach(opt => {
    properties[opt.path] = Type.Union([
      opt.type,
      Type.Object({
        $position: Type.Optional(Type.Number()),
        $each: Type.Optional(Type.Array(opt.type)),
        value: Type.Optional(opt.type)
      }, { additionalProperties: true })
    ])
  })

  return Type.Object(properties, { additionalProperties: false })
}

// Simplified query schema helper - use queryWrapper from @feathersjs/typebox instead
export const createBasicQueryOps = () => {
  return {
    $limit: Type.Optional(Type.Number()),
    $skip: Type.Optional(Type.Number()),
    $sort: Type.Optional(Type.Record(Type.String(), Type.Union([Type.Literal(1), Type.Literal(-1)]))),
    $select: Type.Optional(Type.Array(Type.String()))
  }
}

export const queryWrapper = (schema:any, extensions?:any, options?:any): any => {
  return Type.Partial(Type.Object({...querySyntax(schema, extensions, { additionalProperties: true, ...options }).properties, ...createBasicQueryOps(), ...commonQueriesSchema.properties}))
}

// Utility to convert TypeBox schema to JSON Schema for OpenAI API usage
export const typeboxToJsonSchema = (typeboxSchema: any): any => {
  // This is a simplified converter - TypeBox schemas can be used directly as JSON Schema
  // in many cases, but we need to extract the properties for OpenAI
  if (typeboxSchema && typeboxSchema.properties) {
    return {
      type: 'object',
      properties: typeboxSchema.properties,
      additionalProperties: typeboxSchema.additionalProperties || false
    }
  }
  return typeboxSchema
}

// Email validation and handling
export const IsEmail = (str: string): boolean => {
  return /^(([^<>()[\]\\.,;:\s@']+(\.[^<>()[\]\\.,;:\s@']+)*)|('.+'))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,24}))$/.test(str)
}

type emailHandlerOptions = { throw?: boolean }
export const emailHandler = (options?: emailHandlerOptions) => {
  return async (val: string) => {
    if (val) {
      if (!IsEmail(val) && options?.throw) {
        throw new Error(`Invalid email - ${val}`)
      } else {
        return val.toLowerCase().trim()
      }
    }
    return val
  }
}


// TypeBox addToSet function - supports $push and $addToSet operations
export const addToSet = (opts: Array<{ path: string, type: any }>) => {
  const properties: Record<string, any> = {}

  // Always include updatedByHistory
  const allOpts = [
    { path: 'updatedByHistory', type: updatedByHistoryEntrySchema },
    ...opts
  ]

  // If only updatedByHistory, return generic record
  if (allOpts.length === 1) {
    return Type.Record(Type.String(), Type.Any())
  }

  allOpts.forEach(opt => {
    properties[opt.path] = Type.Union([
      opt.type,
      Type.Object({
        $position: Type.Optional(Type.Number()),
        $each: Type.Optional(Type.Array(opt.type)),
        value: Type.Optional(opt.type)
      }, { additionalProperties: true })
    ])
  })

  return Type.Optional(Type.Object(properties, { additionalProperties: false }))
}

// TypeBox pull function - supports $pull operations
export const pull = (opts: Array<{ path: string, type: any }>) => {
  const properties: Record<string, any> = {}

  // If no options provided, return generic record
  if (opts.length === 0) {
    return Type.Optional(Type.Record(Type.String(), Type.Any()))
  }

  opts.forEach(opt => {
    properties[opt.path] = opt.type
  })

  return Type.Optional(Type.Object(properties, { additionalProperties: true }))
}

// Common patch function - equivalent to the original commonPatch
type CommonPatchOptions = {
  pickedForSet?:any,
  pushPullOpts: Array<{ path: string, type: any }>
}
export const commonPatch = (schemaProperties: any, {pickedForSet, pushPullOpts}: CommonPatchOptions) => {
  const pushPull = pushPullOpts || []
  const setObj = pickedForSet || Type.Object({}, { additionalProperties: true })
  const obj = {
    ...schemaProperties,
    $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
    $set: Type.Optional(setObj),
    $inc: Type.Optional(Type.Record(Type.String(), Type.Number())),
    $push: Type.Optional(createAddToSetSchema([
      { path: 'updatedByHistory', type: updatedByHistoryEntrySchema },
      ...pushPull
    ]))
  }
  if(obj._id) delete obj._id;
  if(pushPull.length){
    if(!obj['$pull']) obj['$pull'] = Type.Optional(pull(pushPull))
    if(!obj['$addToSet']) obj['$addToSet'] = Type.Optional(addToSet(pushPull))
  }
  return Type.Partial(Type.Object(obj, { additionalProperties: false }))
}


export const nullable = {
  string: Type.Union([Type.String(), Type.Null()]),
  boolean: Type.Union([Type.Boolean(), Type.Null()])
} as const;
